import { Injectable, Inject, Logger, NotFoundException, BadRequestException } from '@nestjs/common'
import { eq, and, desc, type InferSelectModel, type InferInsertModel } from 'drizzle-orm'
import { NodePgDatabase } from 'drizzle-orm/node-postgres'

import { ProjectNotificationService } from './services/project-notification.service'
import { ProjectValidationService } from './services/project-validation.service'
import { projects, projectStatusHistory, users, submissionStatusEnum } from '../../libs/database/schema'
import * as schema from '../../libs/database/schema'

// Type definitions for proper type safety
type ProjectSelect = InferSelectModel<typeof projects>

type ProjectInsert = InferInsertModel<typeof projects>

type ProjectUpdate = Partial<ProjectInsert>

// Infer the submission status type from the database schema
type SubmissionStatus = (typeof submissionStatusEnum.enumValues)[number]

const STATUS_TRANSITIONS: Record<SubmissionStatus, SubmissionStatus[]> = {
  draft: ['submitted'],
  submitted: ['under_review', 'needs_revision'],
  under_review: ['needs_revision', 'approved'],
  needs_revision: ['submitted'],
  approved: ['wallet_provided'],
  wallet_provided: ['fee_paid'],
  fee_paid: ['liquidity_provided'],
  liquidity_provided: [], // Final state
}

@Injectable()
export class ProjectsService {
  private readonly logger = new Logger(ProjectsService.name)

  constructor(
    @Inject('DB') private db: NodePgDatabase<typeof schema>,
    private validationService: ProjectValidationService,
    private notificationService: ProjectNotificationService,
  ) {}

  async create(userId: string, projectData: Partial<ProjectInsert>): Promise<ProjectSelect> {
    const insertData = {
      userId,
      submissionStatus: 'draft',
      currentFormStep: 1,
      // Required fields with defaults
      projectName: projectData.projectName || '',
      projectWebsite: projectData.projectWebsite || '',
      contactName: projectData.contactName || '',
      contactEmail: projectData.contactEmail || '',
      roleInProject: projectData.roleInProject || '',
      blockchainToLaunchOn: projectData.blockchainToLaunchOn || 'ethereum',
      totalRaisedUsd: '0.00',
      officialTwitter: projectData.officialTwitter || '',
      tokenName: projectData.tokenName || '',
      tokenSymbol: projectData.tokenSymbol || '',
      tokenContractAddress: projectData.tokenContractAddress || '',
      totalTokenSupply: projectData.totalTokenSupply || '0',
      tokenLaunchMarketCapUsd: projectData.tokenLaunchMarketCapUsd || '0.00',
      tgeType: projectData.tgeType || 'fixed_price',
      allocationTotalSupplyCaishen: projectData.allocationTotalSupplyCaishen || '0.00',
      allocationCirculatingSupplyCaishen: projectData.allocationCirculatingSupplyCaishen || '0.00',
      acceptedCurrenciesForPairing: projectData.acceptedCurrenciesForPairing || 'USDT',
      tgePriceUsdc: projectData.tgePriceUsdc || '0.000000000',
      exclusiveTradingPeriodHours: projectData.exclusiveTradingPeriodHours || 24,
      expectedTgeLaunchDate: (projectData.expectedTgeLaunchDate || new Date()) as any,
      liquidityActivationDate: (projectData.liquidityActivationDate || new Date()) as any,
      minAmountPerTradeSol: projectData.minAmountPerTradeSol || '0.000000000',
      maxAmountPerTradeSol: projectData.maxAmountPerTradeSol || '0.000000000',
      headerDescription: projectData.headerDescription || null,
      teamPublicProfileLinks: projectData.teamPublicProfileLinks || null,
    }

    const newProject = await this.db
      .insert(projects)
      .values(insertData as any)
      .returning()

    this.logger.log(`Created new project ${newProject[0].projectId} for user ${userId}`)

    return newProject[0]
  }

  async findByIdAndUserId(projectId: string, userId: string) {
    const result = await this.db
      .select()
      .from(projects)
      .where(and(eq(projects.projectId, projectId), eq(projects.userId, userId)))
      .limit(1)

    return result[0] || null
  }

  async findById(projectId: string) {
    const result = await this.db.select().from(projects).where(eq(projects.projectId, projectId)).limit(1)

    return result[0] || null
  }

  async findByUserId(userId: string) {
    return this.db.select().from(projects).where(eq(projects.userId, userId)).orderBy(desc(projects.updatedAt))
  }

  async update(projectId: string, userId: string, updateData: ProjectUpdate): Promise<ProjectSelect> {
    const existingProject = await this.findByIdAndUserId(projectId, userId)

    if (!existingProject) {
      throw new NotFoundException('Project not found or access denied')
    }

    const updated = await this.db
      .update(projects)
      .set({
        ...updateData,
        updatedAt: new Date(),
      })
      .where(eq(projects.projectId, projectId))
      .returning()

    this.logger.log(`Updated project ${projectId} for user ${userId}`)

    return updated[0]
  }

  async updateStatus(projectId: string, newStatus: SubmissionStatus, changedBy: string, comment?: string) {
    // Get current project
    const project = await this.findById(projectId)

    if (!project) {
      throw new NotFoundException('Project not found')
    }

    const currentStatus = project.submissionStatus as SubmissionStatus

    // Validate status transition
    if (!this.isValidStatusTransition(currentStatus, newStatus)) {
      throw new BadRequestException(
        `Invalid status transition from ${currentStatus} to ${newStatus}. Allowed transitions: ${STATUS_TRANSITIONS[currentStatus].join(', ')}`,
      )
    }

    // Update project status
    const updated = await this.db
      .update(projects)
      .set({
        submissionStatus: newStatus,
        updatedAt: new Date(),
      })
      .where(eq(projects.projectId, projectId))
      .returning()

    // Record status change in history
    await this.db.insert(projectStatusHistory).values({
      projectId,
      previousStatus: currentStatus,
      newStatus,
      changedBy,
      comment,
    })

    this.logger.log(`Project ${projectId} status changed from ${currentStatus} to ${newStatus} by ${changedBy}`)

    return updated[0]
  }

  async listAllProjects(filters?: { status?: string; blockchain?: string; page?: number; limit?: number }) {
    const page = filters?.page || 1
    const limit = Math.min(filters?.limit || 20, 100) // Max 100 items per page
    const offset = (page - 1) * limit

    // Apply filters
    const whereConditions = []

    if (filters?.status) {
      whereConditions.push(eq(projects.submissionStatus, filters.status as ProjectSelect['submissionStatus']))
    }

    if (filters?.blockchain) {
      whereConditions.push(
        eq(projects.blockchainToLaunchOn, filters.blockchain as ProjectSelect['blockchainToLaunchOn']),
      )
    }

    const results = await this.db
      .select({
        projectId: projects.projectId,
        projectName: projects.projectName,
        submissionStatus: projects.submissionStatus,
        blockchainToLaunchOn: projects.blockchainToLaunchOn,
        contactEmail: projects.contactEmail,
        createdAt: projects.createdAt,
        updatedAt: projects.updatedAt,
      })
      .from(projects)
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)
      .orderBy(desc(projects.createdAt))
      .limit(limit)
      .offset(offset)

    return {
      projects: results,
      pagination: {
        page,
        limit,
        hasMore: results.length === limit,
      },
    }
  }

  private isValidStatusTransition(currentStatus: SubmissionStatus, newStatus: SubmissionStatus): boolean {
    const allowedTransitions = STATUS_TRANSITIONS[currentStatus]

    return allowedTransitions.includes(newStatus)
  }

  async submitProject(projectId: string, userId: string) {
    // Get the project and user
    const project = await this.findByIdAndUserId(projectId, userId)

    if (!project) {
      throw new NotFoundException('Project not found or access denied')
    }

    // Check if project is in draft status
    if (project.submissionStatus !== 'draft') {
      throw new BadRequestException(
        `Cannot submit project with status: ${project.submissionStatus}. Only draft projects can be submitted.`,
      )
    }

    // Validate project completeness using existing validation service
    const validation = this.validationService.validateForSubmission(project)

    if (!validation.isValid) {
      throw new BadRequestException(
        `Project validation failed. Missing required fields: ${validation.missingFields.join(', ')}`,
      )
    }

    // Get user information for email
    const userResult = await this.db.select().from(users).where(eq(users.id, userId)).limit(1)

    if (userResult.length === 0) {
      throw new NotFoundException('User not found')
    }

    const user = userResult[0]

    // Update project status to submitted
    const updatedProject = await this.updateStatus(projectId, 'submitted', userId, 'Project submitted for review')

    // Send email notifications with improved error handling
    let emailWarning: string | undefined
    try {
      await this.notificationService.sendProjectSubmissionEmails(project, user)
      this.logger.log(`Sent submission notification emails for project ${projectId}`)
    } catch (error) {
      emailWarning =
        'Project submitted successfully, but confirmation email delivery failed. Please check your email address.'
      this.logger.error(`Failed to send submission emails for project ${projectId}:`, error)
      // Don't fail the submission if email fails - just log the error and warn user
    }

    return {
      success: true,
      message: 'Project submitted successfully. You will receive a confirmation email shortly.',
      warning: emailWarning,
      project: {
        projectId: updatedProject.projectId,
        projectName: updatedProject.projectName,
        submissionStatus: updatedProject.submissionStatus,
        submittedAt: updatedProject.updatedAt,
      },
    }
  }

  async submitWalletAddress(projectId: string, walletAddress: string, userId: string) {
    // Get the project - verify user ownership
    const project = await this.findByIdAndUserId(projectId, userId)

    if (!project) {
      throw new NotFoundException('Project not found or access denied')
    }

    // Check if project is in approved status
    if (project.submissionStatus !== 'approved') {
      throw new BadRequestException(
        `Cannot submit wallet address for project with status: ${project.submissionStatus}. Only approved projects can have wallet addresses submitted.`,
      )
    }

    // Check if wallet address is already provided
    if (project.providedWalletAddress) {
      throw new BadRequestException('Wallet address has already been provided for this project')
    }

    // Get user information for email
    const userResult = await this.db.select().from(users).where(eq(users.id, project.userId)).limit(1)

    if (userResult.length === 0) {
      throw new NotFoundException('Project owner not found')
    }

    const user = userResult[0]

    // Update project with wallet address and change status to wallet_provided
    const updatedProject = await this.db
      .update(projects)
      .set({
        providedWalletAddress: walletAddress,
        submissionStatus: 'wallet_provided',
        updatedAt: new Date(),
      })
      .where(eq(projects.projectId, projectId))
      .returning()

    // Record status change in history
    await this.db.insert(projectStatusHistory).values({
      projectId,
      previousStatus: 'approved',
      newStatus: 'wallet_provided',
      changedBy: 'system', // This is a public endpoint
      comment: `Wallet address provided: ${walletAddress}`,
    })

    this.logger.log(`Wallet address submitted for project ${projectId}: ${walletAddress}`)

    // Send email notification with improved error handling
    let emailWarning: string | undefined
    try {
      await this.notificationService.sendWalletSubmissionEmail(
        {
          ...project,
          expectedTgeLaunchDate: project.expectedTgeLaunchDate
            ? new Date(project.expectedTgeLaunchDate).toISOString()
            : new Date().toISOString(),
          tokenSymbol: project.tokenSymbol,
        },
        user,
      )
      this.logger.log(`Sent wallet submission notification email for project ${projectId}`)
    } catch (error) {
      emailWarning =
        'Wallet address submitted successfully, but confirmation email delivery failed. Please check your email address.'
      this.logger.error(`Failed to send wallet submission email for project ${projectId}:`, error)
      // Don't fail the submission if email fails - just log the error and warn user
    }

    return {
      success: true,
      message: 'Wallet address submitted successfully. You will receive a confirmation email shortly.',
      warning: emailWarning,
      project: {
        projectId: updatedProject[0].projectId,
        projectName: updatedProject[0].projectName,
        tokenSymbol: updatedProject[0].tokenSymbol,
        submissionStatus: updatedProject[0].submissionStatus,
        providedWalletAddress: updatedProject[0].providedWalletAddress,
        updatedAt: updatedProject[0].updatedAt,
      },
    }
  }

  async delete(projectId: string, userId: string) {
    const existingProject = await this.findByIdAndUserId(projectId, userId)

    if (!existingProject) {
      throw new NotFoundException('Project not found or access denied')
    }

    // Only allow deletion of draft projects
    if (existingProject.submissionStatus !== 'draft') {
      throw new BadRequestException('Only draft projects can be deleted')
    }

    await this.db.delete(projects).where(eq(projects.projectId, projectId))

    this.logger.log(`Deleted project ${projectId} for user ${userId}`)
  }
}
